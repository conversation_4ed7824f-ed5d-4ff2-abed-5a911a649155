{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projet/Learning/NextJS/nextjs-dashboard/app/lib/placeholder-data.ts"], "sourcesContent": ["// This file contains placeholder data that you'll be replacing with real data in the Data Fetching chapter:\n// https://nextjs.org/learn/dashboard-app/fetching-data\nconst users = [\n  {\n    id: '410544b2-4001-4271-9855-fec4b6a6442a',\n    name: 'User',\n    email: '<EMAIL>',\n    password: '123456',\n  },\n];\n\nconst customers = [\n  {\n    id: 'd6e15727-9fe1-4961-8c5b-ea44a9bd81aa',\n    name: 'Evil Rabbit',\n    email: '<EMAIL>',\n    image_url: '/customers/evil-rabbit.png',\n  },\n  {\n    id: '3958dc9e-712f-4377-85e9-fec4b6a6442a',\n    name: '<PERSON><PERSON>',\n    email: '<EMAIL>',\n    image_url: '/customers/delba-de-oliveira.png',\n  },\n  {\n    id: '3958dc9e-742f-4377-85e9-fec4b6a6442a',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    image_url: '/customers/lee-robinson.png',\n  },\n  {\n    id: '76d65c26-f784-44a2-ac19-586678f7c2f2',\n    name: '<PERSON> <PERSON>otny',\n    email: '<EMAIL>',\n    image_url: '/customers/michael-novotny.png',\n  },\n  {\n    id: 'CC27C14A-0ACF-4F4A-A6C9-D45682C144B9',\n    name: 'Amy <PERSON>',\n    email: '<EMAIL>',\n    image_url: '/customers/amy-burns.png',\n  },\n  {\n    id: '13D07535-C59E-4157-A011-F8D2EF4E0CBB',\n    name: 'Balazs Orban',\n    email: '<EMAIL>',\n    image_url: '/customers/balazs-orban.png',\n  },\n];\n\nconst invoices = [\n  {\n    customer_id: customers[0].id,\n    amount: 15795,\n    status: 'pending',\n    date: '2022-12-06',\n  },\n  {\n    customer_id: customers[1].id,\n    amount: 20348,\n    status: 'pending',\n    date: '2022-11-14',\n  },\n  {\n    customer_id: customers[4].id,\n    amount: 3040,\n    status: 'paid',\n    date: '2022-10-29',\n  },\n  {\n    customer_id: customers[3].id,\n    amount: 44800,\n    status: 'paid',\n    date: '2023-09-10',\n  },\n  {\n    customer_id: customers[5].id,\n    amount: 34577,\n    status: 'pending',\n    date: '2023-08-05',\n  },\n  {\n    customer_id: customers[2].id,\n    amount: 54246,\n    status: 'pending',\n    date: '2023-07-16',\n  },\n  {\n    customer_id: customers[0].id,\n    amount: 666,\n    status: 'pending',\n    date: '2023-06-27',\n  },\n  {\n    customer_id: customers[3].id,\n    amount: 32545,\n    status: 'paid',\n    date: '2023-06-09',\n  },\n  {\n    customer_id: customers[4].id,\n    amount: 1250,\n    status: 'paid',\n    date: '2023-06-17',\n  },\n  {\n    customer_id: customers[5].id,\n    amount: 8546,\n    status: 'paid',\n    date: '2023-06-07',\n  },\n  {\n    customer_id: customers[1].id,\n    amount: 500,\n    status: 'paid',\n    date: '2023-08-19',\n  },\n  {\n    customer_id: customers[5].id,\n    amount: 8945,\n    status: 'paid',\n    date: '2023-06-03',\n  },\n  {\n    customer_id: customers[2].id,\n    amount: 1000,\n    status: 'paid',\n    date: '2022-06-05',\n  },\n];\n\nconst revenue = [\n  { month: 'Jan', revenue: 2000 },\n  { month: 'Feb', revenue: 1800 },\n  { month: 'Mar', revenue: 2200 },\n  { month: 'Apr', revenue: 2500 },\n  { month: 'May', revenue: 2300 },\n  { month: 'Jun', revenue: 3200 },\n  { month: 'Jul', revenue: 3500 },\n  { month: 'Aug', revenue: 3700 },\n  { month: 'Sep', revenue: 2500 },\n  { month: 'Oct', revenue: 2800 },\n  { month: 'Nov', revenue: 3000 },\n  { month: 'Dec', revenue: 4800 },\n];\n\nexport { users, customers, invoices, revenue };\n"], "names": [], "mappings": "AAAA,4GAA4G;AAC5G,uDAAuD;;;;;;;AACvD,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAED,MAAM,WAAW;IACf;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA;QACE,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;QAC5B,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,UAAU;IACd;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;IAC9B;QAAE,OAAO;QAAO,SAAS;IAAK;CAC/B", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projet/Learning/NextJS/nextjs-dashboard/app/seed/route.ts"], "sourcesContent": ["import bcrypt from 'bcrypt';\nimport postgres from 'postgres';\nimport { invoices, customers, revenue, users } from '../lib/placeholder-data';\n\nconst sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require' });\n\nasync function seedUsers() {\n  await sql`CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"`;\n  await sql`\n    CREATE TABLE IF NOT EXISTS users (\n      id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      email TEXT NOT NULL UNIQUE,\n      password TEXT NOT NULL\n    );\n  `;\n\n  const insertedUsers = await Promise.all(\n    users.map(async (user) => {\n      const hashedPassword = await bcrypt.hash(user.password, 10);\n      return sql`\n        INSERT INTO users (id, name, email, password)\n        VALUES (${user.id}, ${user.name}, ${user.email}, ${hashedPassword})\n        ON CONFLICT (id) DO NOTHING;\n      `;\n    }),\n  );\n\n  return insertedUsers;\n}\n\nasync function seedInvoices() {\n  await sql`CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"`;\n\n  await sql`\n    CREATE TABLE IF NOT EXISTS invoices (\n      id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,\n      customer_id UUID NOT NULL,\n      amount INT NOT NULL,\n      status VARCHAR(255) NOT NULL,\n      date DATE NOT NULL\n    );\n  `;\n\n  const insertedInvoices = await Promise.all(\n    invoices.map(\n      (invoice) => sql`\n        INSERT INTO invoices (customer_id, amount, status, date)\n        VALUES (${invoice.customer_id}, ${invoice.amount}, ${invoice.status}, ${invoice.date})\n        ON CONFLICT (id) DO NOTHING;\n      `,\n    ),\n  );\n\n  return insertedInvoices;\n}\n\nasync function seedCustomers() {\n  await sql`CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"`;\n\n  await sql`\n    CREATE TABLE IF NOT EXISTS customers (\n      id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      email VARCHAR(255) NOT NULL,\n      image_url VARCHAR(255) NOT NULL\n    );\n  `;\n\n  const insertedCustomers = await Promise.all(\n    customers.map(\n      (customer) => sql`\n        INSERT INTO customers (id, name, email, image_url)\n        VALUES (${customer.id}, ${customer.name}, ${customer.email}, ${customer.image_url})\n        ON CONFLICT (id) DO NOTHING;\n      `,\n    ),\n  );\n\n  return insertedCustomers;\n}\n\nasync function seedRevenue() {\n  await sql`\n    CREATE TABLE IF NOT EXISTS revenue (\n      month VARCHAR(4) NOT NULL UNIQUE,\n      revenue INT NOT NULL\n    );\n  `;\n\n  const insertedRevenue = await Promise.all(\n    revenue.map(\n      (rev) => sql`\n        INSERT INTO revenue (month, revenue)\n        VALUES (${rev.month}, ${rev.revenue})\n        ON CONFLICT (month) DO NOTHING;\n      `,\n    ),\n  );\n\n  return insertedRevenue;\n}\n\nexport async function GET() {\n  try {\n    const result = await sql.begin((sql) => [\n      seedUsers(),\n      seedCustomers(),\n      seedInvoices(),\n      seedRevenue(),\n    ]);\n\n    return Response.json({ message: 'Database seeded successfully' });\n  } catch (error) {\n    return Response.json({ error }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,MAAM,CAAA,GAAA,+LAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAG;IAAE,KAAK;AAAU;AAEjE,eAAe;IACb,MAAM,GAAG,CAAC,0CAA0C,CAAC;IACrD,MAAM,GAAG,CAAC;;;;;;;EAOV,CAAC;IAED,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,mIAAA,CAAA,QAAK,CAAC,GAAG,CAAC,OAAO;QACf,MAAM,iBAAiB,MAAM,qGAAA,CAAA,UAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;QACxD,OAAO,GAAG,CAAC;;gBAED,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,eAAe;;MAEpE,CAAC;IACH;IAGF,OAAO;AACT;AAEA,eAAe;IACb,MAAM,GAAG,CAAC,0CAA0C,CAAC;IAErD,MAAM,GAAG,CAAC;;;;;;;;EAQV,CAAC;IAED,MAAM,mBAAmB,MAAM,QAAQ,GAAG,CACxC,mIAAA,CAAA,WAAQ,CAAC,GAAG,CACV,CAAC,UAAY,GAAG,CAAC;;gBAEP,EAAE,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC;;MAEvF,CAAC;IAIL,OAAO;AACT;AAEA,eAAe;IACb,MAAM,GAAG,CAAC,0CAA0C,CAAC;IAErD,MAAM,GAAG,CAAC;;;;;;;EAOV,CAAC;IAED,MAAM,oBAAoB,MAAM,QAAQ,GAAG,CACzC,mIAAA,CAAA,YAAS,CAAC,GAAG,CACX,CAAC,WAAa,GAAG,CAAC;;gBAER,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,SAAS,SAAS,CAAC;;MAEpF,CAAC;IAIL,OAAO;AACT;AAEA,eAAe;IACb,MAAM,GAAG,CAAC;;;;;EAKV,CAAC;IAED,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CACvC,mIAAA,CAAA,UAAO,CAAC,GAAG,CACT,CAAC,MAAQ,GAAG,CAAC;;gBAEH,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,OAAO,CAAC;;MAEtC,CAAC;IAIL,OAAO;AACT;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,IAAI,KAAK,CAAC,CAAC,MAAQ;gBACtC;gBACA;gBACA;gBACA;aACD;QAED,OAAO,SAAS,IAAI,CAAC;YAAE,SAAS;QAA+B;IACjE,EAAE,OAAO,OAAO;QACd,OAAO,SAAS,IAAI,CAAC;YAAE;QAAM,GAAG;YAAE,QAAQ;QAAI;IAChD;AACF", "debugId": null}}]}