# Copy from .env.local on the Vercel dashboard
# https://nextjs.org/learn/dashboard-app/setting-up-your-database#create-a-postgres-database
POSTGRES_URL="postgres://postgres:5bdcfwg5@localhost:5432/nextjs-dashboard-postgres"
POSTGRES_URL_NON_POOLING="postgres://postgres:5bdcfwg5@localhost:5432/nextjs-dashboard-postgres"
POSTGRES_USER="postgres"
POSTGRES_HOST="localhost"
POSTGRES_PASSWORD="5bdcfwg5"
POSTGRES_DATABASE="nextjs-dashboard-postgres"

# `openssl rand -base64 32`
AUTH_SECRET=
AUTH_URL=http://localhost:3000/api/auth